const express = require('express');
const cors = require('cors');
const axios = require('axios');
const fs = require('fs');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 3000;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.static('.'));

// Load knowledge base
let knowledgeBase = "";
try {
    const data = JSON.parse(fs.readFileSync('data.json', 'utf8'));
    knowledgeBase = Object.values(data).join('\n\n');
    console.log('✅ Đã load dữ liệu từ Technojet');
} catch (error) {
    console.error('❌ Lỗi load dữ liệu:', error.message);
}

// Rate limiting
const rateLimitMap = new Map();
const RATE_LIMIT = 10; // 10 requests per minute
const RATE_WINDOW = 60 * 1000; // 1 minute

function checkRateLimit(ip) {
    const now = Date.now();
    const userRequests = rateLimitMap.get(ip) || [];
    
    // Remove old requests
    const validRequests = userRequests.filter(time => now - time < RATE_WINDOW);
    
    if (validRequests.length >= RATE_LIMIT) {
        return false;
    }
    
    validRequests.push(now);
    rateLimitMap.set(ip, validRequests);
    return true;
}

// Input validation
function validateInput(message, file) {
    if (!message || typeof message !== 'string') {
        throw new Error('Tin nhắn không hợp lệ');
    }
    
    if (message.length > 1000) {
        throw new Error('Tin nhắn quá dài (tối đa 1000 ký tự)');
    }
    
    if (file && file.data) {
        if (!file.mime_type || !file.mime_type.startsWith('image/')) {
            throw new Error('Chỉ hỗ trợ file hình ảnh');
        }
        
        // Check base64 size (approximate file size)
        const sizeInBytes = (file.data.length * 3) / 4;
        if (sizeInBytes > 5 * 1024 * 1024) { // 5MB limit
            throw new Error('File quá lớn (tối đa 5MB)');
        }
    }
}

// Content filtering
function filterContent(message) {
    const forbiddenWords = ['hack', 'virus', 'malware', 'spam'];
    const lowerMessage = message.toLowerCase();
    
    for (const word of forbiddenWords) {
        if (lowerMessage.includes(word)) {
            throw new Error('Nội dung không phù hợp');
        }
    }
}

// API endpoint for chat
app.post('/api/chat', async (req, res) => {
    try {
        const clientIP = req.ip || req.connection.remoteAddress;
        
        // Rate limiting
        if (!checkRateLimit(clientIP)) {
            return res.status(429).json({
                error: 'Quá nhiều yêu cầu. Vui lòng thử lại sau.'
            });
        }
        
        const { message, file, chatHistory = [] } = req.body;
        
        // Validate input
        validateInput(message, file);
        filterContent(message);
        
        // Prepare API request
        const parts = [{
            text: `Bạn là trợ lý AI của Technojet. Chỉ trả lời ngắn gọn xúc tích nhưng thân thiện và không cứng nhắc các câu hỏi liên quan đến Technojet dựa trên cơ sở dữ liệu sau. 
            Câu trả lời tránh dùng các ký tự đặt biệt như * chỉ dùng "." và ",". 
            Dữ liệu nội bộ của Technojet: ${knowledgeBase}
            Câu hỏi của người dùng: ${message}`
        }];
        
        if (file && file.data && file.mime_type) {
            parts.push({
                inline_data: {
                    mime_type: file.mime_type,
                    data: file.data
                }
            });
        }
        
        const requestBody = {
            contents: [
                ...chatHistory,
                {
                    role: "user",
                    parts: parts
                }
            ]
        };
        
        // Call Gemini API
        const response = await axios.post(
            `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent?key=${process.env.GEMINI_API_KEY}`,
            requestBody,
            {
                headers: { 'Content-Type': 'application/json' },
                timeout: 30000 // 30 seconds timeout
            }
        );
        
        if (!response.data.candidates || !response.data.candidates[0]) {
            throw new Error('Không nhận được phản hồi từ AI');
        }
        
        const aiResponse = response.data.candidates[0].content.parts[0].text.trim();
        
        res.json({
            success: true,
            response: aiResponse
        });
        
    } catch (error) {
        console.error('API Error:', error.message);
        
        let errorMessage = 'Đã xảy ra lỗi. Vui lòng thử lại.';
        
        if (error.response && error.response.status === 429) {
            errorMessage = 'Hệ thống đang quá tải. Vui lòng thử lại sau.';
        } else if (error.code === 'ECONNABORTED') {
            errorMessage = 'Yêu cầu quá lâu. Vui lòng thử lại.';
        } else if (error.message.includes('API key')) {
            errorMessage = 'Lỗi cấu hình hệ thống.';
        }
        
        res.status(500).json({
            success: false,
            error: errorMessage
        });
    }
});

// Health check endpoint
app.get('/api/health', (req, res) => {
    res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        knowledgeBaseLoaded: knowledgeBase.length > 0
    });
});

app.listen(PORT, () => {
    console.log(`🚀 Server đang chạy tại http://localhost:${PORT}`);
});
