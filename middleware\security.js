// Security middleware cho chatbot
const rateLimit = require('express-rate-limit');
const helmet = require('helmet');

// Rate limiting cho API chat
const chatLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 phút
    max: 10, // 10 requests per minute
    message: {
        error: 'Quá nhiều yêu cầu. Vui lòng thử lại sau 1 phút.'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Rate limiting cho upload file
const uploadLimiter = rateLimit({
    windowMs: 60 * 1000, // 1 phút
    max: 5, // 5 uploads per minute
    message: {
        error: 'Quá nhiều file upload. Vui lòng thử lại sau.'
    }
});

// Content Security Policy
const cspOptions = {
    directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net", "https://fonts.googleapis.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
        fontSrc: ["'self'", "https://fonts.gstatic.com"],
        imgSrc: ["'self'", "data:", "blob:"],
        connectSrc: ["'self'", "https://generativelanguage.googleapis.com"]
    }
};

// Input sanitization
function sanitizeInput(input) {
    if (typeof input !== 'string') return input;
    
    // Remove potentially dangerous characters
    return input
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/on\w+\s*=/gi, '')
        .trim();
}

// Validate file upload
function validateFileUpload(file) {
    if (!file) return true;
    
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    const maxSize = 5 * 1024 * 1024; // 5MB
    
    if (!allowedTypes.includes(file.mime_type)) {
        throw new Error('Chỉ hỗ trợ file hình ảnh (JPEG, PNG, GIF, WebP)');
    }
    
    // Estimate file size from base64
    const sizeInBytes = (file.data.length * 3) / 4;
    if (sizeInBytes > maxSize) {
        throw new Error('File quá lớn. Tối đa 5MB.');
    }
    
    return true;
}

// Check for suspicious patterns
function detectSuspiciousContent(message) {
    const suspiciousPatterns = [
        /\b(hack|crack|exploit|vulnerability)\b/i,
        /\b(password|admin|root|sudo)\b/i,
        /\b(sql\s*injection|xss|csrf)\b/i,
        /\b(malware|virus|trojan)\b/i,
        /<script|javascript:|data:text\/html/i,
        /\b(eval|exec|system|shell)\b/i
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(message));
}

module.exports = {
    chatLimiter,
    uploadLimiter,
    cspOptions,
    sanitizeInput,
    validateFileUpload,
    detectSuspiciousContent,
    helmet: helmet({
        contentSecurityPolicy: cspOptions,
        crossOriginEmbedderPolicy: false
    })
};
