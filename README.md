# Technojet AI Chatbot

Hệ thống chatbot AI cho công ty Technojet, sử dụng Google Gemini API để trả lời câu hỏi dựa trên dữ liệu từ website.

## 🚀 Cài đặt và Chạy

### 1. Cài đặt dependencies
```bash
npm install
```

### 2. Cấu hình API Key
```bash
# Copy file .env.example thành .env
cp .env.example .env

# Chỉnh sửa file .env và thêm API key của bạn
GEMINI_API_KEY=your_actual_api_key_here
```

### 3. Crawl dữ liệu (nế<PERSON> cần)
```bash
npm run crawl
```

### 4. Chạy server
```bash
# Production
npm start

# Development (với nodemon)
npm run dev
```

### 5. Truy cập ứng dụng
Mở trình duyệt và truy cập: `http://localhost:3000`

## 📁 Cấu trúc Project

```
├── api-server.js      # Server API chính
├── server.js          # Script crawl dữ liệu
├── index.html         # Giao diện chatbot
├── data.json          # Dữ liệu đã crawl
├── package.json       # Dependencies
├── .env               # Cấu hình (không commit)
└── .env.example       # Template cấu hình
```

## 🔧 Tính năng

- ✅ Chat với AI dựa trên dữ liệu Technojet
- ✅ Upload và phân tích hình ảnh
- ✅ Rate limiting (10 requests/phút)
- ✅ Validation input
- ✅ Xử lý lỗi chi tiết
- ✅ Bảo mật API key
- ✅ Responsive design

## 🛡️ Bảo mật

- API key được lưu trong file .env (không public)
- Rate limiting để tránh spam
- Validation input nghiêm ngặt
- Content filtering
- File size limits (5MB)

## 🔄 API Endpoints

### POST /api/chat
Gửi tin nhắn đến AI

**Request:**
```json
{
  "message": "Câu hỏi của user",
  "file": {
    "data": "base64_string",
    "mime_type": "image/jpeg"
  },
  "chatHistory": []
}
```

**Response:**
```json
{
  "success": true,
  "response": "Phản hồi từ AI"
}
```

### GET /api/health
Kiểm tra trạng thái server

## 📝 Lưu ý

1. **API Key**: Cần có Google Gemini API key hợp lệ
2. **Rate Limit**: Mỗi IP chỉ được 10 requests/phút
3. **File Upload**: Chỉ hỗ trợ hình ảnh, tối đa 5MB
4. **Data**: Chạy `npm run crawl` để cập nhật dữ liệu mới

## 🐛 Troubleshooting

### Lỗi "API key not found"
- Kiểm tra file .env có tồn tại
- Đảm bảo GEMINI_API_KEY được set đúng

### Lỗi "Rate limit exceeded"
- Chờ 1 phút rồi thử lại
- Hoặc tăng RATE_LIMIT trong .env

### Lỗi "Knowledge base not loaded"
- Chạy `npm run crawl` để tạo data.json
- Kiểm tra file data.json có tồn tại

## 📞 Liên hệ

- **Công ty**: TechnoJet
- **Email**: <EMAIL>
- **Hotline**: 0933 127 743
