<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <!-- Link font google -->
    <link rel="stylesheet"
        href="https://fonts.googleapis.com/css2?family=Material+Symbols+Rounded:opsz,wght,FILL,GRAD@24,400,0,0" />
    <style>
        /* Import Google Fonts-Inter */
        @import url('https://fonts.googleapis.com/css2?family=Inter:opsz,wght@14..32,100..900&display=swap');

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: "Inter", sans-serif;
        }

        body {

            min-height: 100vh;
            background: linear-gradient(#27613b, #ce9f37);
        }

        #chatbot-toggler {
            position: fixed;
            bottom: 30px;
            right: 35px;
            border: none;
            height: 50px;
            width: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            border-radius: 50%;
            background: #27613b;
            transition: all 0.2s ease;
        }

        body.show-chatbot #chatbot-toggler {
            transform: rotate(90deg);
        }

        #chatbot-toggler span {
            color: #fff;
            position: absolute;
        }

        body.show-chatbot #chatbot-toggler span:first-child,
        #chatbot-toggler span:last-child {
            opacity: 0;
        }

        body.show-chatbot #chatbot-toggler span:last-child {
            opacity: 1;
        }

        .chatbot-popup {
            position: fixed;
            right: 35px;
            bottom: 90px;
            width: 420px;
            background: #fff;
            overflow: hidden;
            border-radius: 15px;
            opacity: 0;
            transform: scale(0.2);
            transform-origin: bottom right;
            pointer-events: none;
            box-shadow: 0cap;
            transition: all 0.1s ease;
        }

        body.show-chatbot .chatbot-popup {
            opacity: 1;
            pointer-events: auto;
            transform: scale(1);
        }

        .chat-header {
            display: flex;
            align-items: center;
            background: #27613b;
            padding: 15px 22px;
            justify-content: space-between;
        }

        .chat-header .header-infor {
            display: flex;
            gap: 10px;
            align-items: center;

        }

        .header-infor .chatbot-logo {
            height: 35px;
            width: 35px;
            padding: 6px;
            fill: #27613b;
            flex-shrink: 0;
            background: #fff;
            border-radius: 50%;
        }

        .header-infor .logo-text {
            color: #fff;
            font-size: 1.31rem;
            font-weight: 600;
        }

        .chat-header #close-chatbot {
            border: none;
            color: #fff;
            height: 40px;
            width: 40px;
            font-size: 1.9rem;
            margin-right: -10px;
            padding-top: 2px;
            cursor: pointer;
            border-radius: 50%;
            background: none;
            transition: 0.2s ease;
        }

        .chat-header #close-chatbot:hover {
            background: #a1df99;
        }

        .chat-body {
            padding: 25px 22px;
            margin-bottom: 82px;
            display: flex;
            gap: 20px;
            height: 460px;
            overflow-y: auto;
            flex-direction: column;
            scrollbar-width: thin;
            scrollbar-color: #27613b transparent;
        }

        .chat-body .message {
            display: flex;
            gap: 11px;
            align-items: center;
        }

        .chat-body .bot-message .bot-avatar {
            height: 35px;
            width: 35px;
            padding: 6px;
            fill: #fff;
            flex-shrink: 0;
            margin-bottom: 2px;
            align-self: flex-end;
            background: #27613b;
            border-radius: 50%;
        }

        .chat-body .user-message {
            flex-direction: column;
            align-items: flex-end;
        }

        .chat-body .message .message-text {
            padding: 12px 16px;
            max-width: 75%;
            font-size: 0.95rem;
        }

        .chat-body .bot-message.thinking .message-text {
            padding: 2px 16px;
        }

        .chat-body .bot-message .message-text {
            background: #a1df99;
            border-radius: 13px 13px 13px 3px;
        }

        .chat-body .user-message .message-text {
            color: #fff;
            background: #27613b;
            border-radius: 13px 13px 3px 13px;
        }

        .chat-body .user-message .attachment {
            width: 50%;
            margin-top: -7px;
            border-radius: 13px 3px 13px 13px;
        }

        .chat-body .bot-message .thinking-indicator {
            display: flex;
            gap: 4px;
            padding-block: 15px;
        }

        .chat-body .bot-message .thinking-indicator .dot {
            height: 7px;
            width: 7px;
            opacity: 0.7;
            border-radius: 50%;
            background: #27613b;
            animation: dotPulse 1.8s ease-in-out infinite;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(1) {
            animation-delay: 0.2s;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(2) {
            animation-delay: 0.3s;
        }

        .chat-body .bot-message .thinking-indicator .dot:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes dotPulse {

            0%,
            44% {
                transform: translateY(0);
            }

            28% {
                opacity: 0.4;
                transform: translateY(-4px);
            }

            44% {
                opacity: 0.2;
            }
        }

        .chat-footer {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: #fff;
            padding: 15px 22px 20px;
        }

        .chat-footer .chat-form {
            display: flex;
            position: relative;
            align-items: center;
            background: #fff;
            border-radius: 32px;
            outline: 1px solid #cccce5;
        }

        .chat-footer .chat-form:focus-within {
            outline: 2px solid #5350c4;
        }

        .chat-form .message-input {
            border: none;
            outline: none;
            height: 47px;
            width: 100%;
            resize: none;
            max-height: 180px;
            white-space: pre-line;
            font-size: 0.95rem;
            padding: 14px 0 13px 18px;
            border-radius: inherit;
            scrollbar-width: think;
            scrollbar-color: transparent transparent;
        }

        .chat-form .message-input:hover {
            scrollbar-color: #27613b transparent;
        }

        .chat-form .chat-controls {
            display: flex;
            height: 47px;
            gap: 3px;
            align-items: center;
            align-self: flex-end;
            padding-right: 6px;
        }

        .chat-form .chat-controls button {
            height: 35px;
            width: 35px;
            border: none;
            font-size: 1.15rem;
            cursor: pointer;
            color: #3f9934;
            background: none;
            border-radius: 50%;
            transition: 0.2s ease;
        }

        .chat-form .chat-controls #send-message {
            color: #fff;
            display: none;
            background: #27613b;
        }

        .chat-form .message-input:valid~.chat-controls #send-message {
            display: block;
        }

        .chat-form .chat-controls #send-message:hover {
            background: #3f9934;
        }

        .chat-form .chat-controls button:hover {
            background: #f1f1ff;
        }

        .chat-form .file-upload-wrapper {
            height: 35px;
            width: 35px;
            position: relative;
        }

        .chat-form .file-upload-wrapper :where(img, button) {
            position: absolute;
        }

        .chat-form .file-upload-wrapper img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .chat-form .file-upload-wrapper #file-cancel {
            color: #ff0000;
            background: #fff;
        }

        .chat-form .file-upload-wrapper :where(img, #file-cancel),
        .chat-form .file-upload-wrapper.file-uploaded #file-upload {
            display: none;
        }

        .chat-form .file-upload-wrapper.file-uploaded img,
        .chat-form .file-upload-wrapper.file-uploaded:hover #file-cancel {
            display: block;
        }

        em-emoji-picker {
            position: absolute;
            left: 50%;
            top: -337px;
            width: 100%;
            max-width: 350px;
            max-height: 300px;
            visibility: hidden;
            transform: translateX(-50%);
        }

        body.show-emoji-picker em-emoji-picker {
            visibility: visible;
        }

        @media (max-width:520px) {
            #chatbot-toggler {
                right: 20px;
                bottom: 20px;
            }

            .chatbot-popup {
                right: 0;
                bottom: 0;
                height: 100%;
                border-radius: 0;
                width: 100%;
            }

            .chatbot-popup .chat-header {
                padding: 12px 15px;
            }

            .chat-body {
                height: cal(90%-55px);
                padding: 25px 15px;
            }

            .chat-footer {
                padding: 10px 15px 15px;
            }

            .chat-form .file-upload-wrapper.file-upload #file-cancel {
                opacity: 0;
            }
        }
    </style>
</head>

<body>
    <button id="chatbot-toggler">
        <span class="material-symbols-rounded">mode_comment</span>
        <span class="material-symbols-rounded">close</span>
    </button>
    <div class="chatbot-popup">
        <!-- Chatbot header -->
        <div class="chat-header">
            <div class="header-infor">
                <svg class="chatbot-logo" xmlns="http://www.w3.org/2000/svg" width="50" height="50"
                    viewBox="0 0 1024 1024">
                    <path
                        d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z">
                    </path>
                </svg>
                <h2 class="logo-text">Technojet AI assistant</h2>
            </div>
            <button id="close-chatbot" class="material-symbols-rounded">
                keyboard_arrow_down
            </button>
        </div>
        <!-- Chatbot body -->
        <div class="chat-body">
            <div class="message bot-message">
                <svg class="bot-avatar" xmlns="http://www.w3.org/2000/svg" width="50" height="50"
                    viewBox="0 0 1024 1024">
                    <path
                        d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z">
                    </path>
                </svg>
                <div class="message-text">Xin chào! <br>Tôi có thể giúp gì cho bạn</div>
            </div>
        </div>
        <!-- Chatbot footer -->
        <div class="chat-footer">
            <form action="#" class="chat-form">
                <textarea placeholder="Message..." class="message-input" required></textarea>
                <div class="chat-controls">
                    <button type="button" id="emoji-picker" class="material-symbols-rounded">
                        sentiment_satisfied
                    </button>
                    <div class="file-upload-wrapper">
                        <input type="file" accept="images/*" id="file-input" hidden>
                        <img src="#">
                        <button type="button" id="file-upload" class="material-symbols-rounded">attach_file</button>
                        <button type="button" id="file-cancel" class="material-symbols-rounded">close</button>
                    </div>
                    <button type="submit" id="send-message" class="material-symbols-rounded">
                        arrow_upward
                    </button>
                </div>
            </form>
        </div>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/emoji-mart@latest/dist/browser.js"></script>
    <script>
        let knowledgeBase = "";
        fetch("data.json")
            .then(res => res.json())
            .then(data => {
                // Gộp tất cả nội dung vào một đoạn văn bản dài
                knowledgeBase = Object.values(data).join("\n\n");
                console.log("✅ Đã load dữ liệu từ Technojet");
            });
        const chatBody = document.querySelector(".chat-body");
        const messageInput = document.querySelector(".message-input");
        const sendMessageButton = document.querySelector("#send-message");
        const fileInput = document.querySelector("#file-input");
        const fileUploadWrapper = document.querySelector(".file-upload-wrapper");
        const fileCancelButton = document.querySelector("#file-cancel");
        const chatbotToggler = document.querySelector("#chatbot-toggler");
        const closeChatbot = document.querySelector("#close-chatbot");
        // Sử dụng API endpoint local thay vì gọi trực tiếp Gemini
        const API_URL = '/api/chat';
        const userData = {
            message: null,
            file: {
                data: null,
                mine_type: null
            }
        }
        const chatHistory = [];
        const initialInputHeight = messageInput.scrollHeight;
        const createMessageElement = (content, ...classes) => {
            const div = document.createElement("div");
            div.classList.add("message", ...classes);
            div.innerHTML = content;
            return div;
        }
        const generateBotResponse = async (incomingMessageDiv) => {
            const messageElement = incomingMessageDiv.querySelector(".message-text");
            const userQuestion = userData.message;

            // Chuẩn bị dữ liệu gửi lên server
            const requestData = {
                message: userQuestion,
                file: userData.file?.data ? {
                    data: userData.file.data,
                    mime_type: userData.file.mime_type
                } : null,
                chatHistory: chatHistory
            };

            const requestOptions = {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(requestData)
            };

            try {
                const response = await fetch(API_URL, requestOptions);
                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'Đã xảy ra lỗi');
                }

                if (!data.success) {
                    throw new Error(data.error || 'Không nhận được phản hồi');
                }

                const apiResponseText = data.response;
                messageElement.innerText = apiResponseText;

                // ✅ Lưu lại lịch sử chat
                chatHistory.push({
                    role: "user",
                    parts: [{ text: userQuestion }]
                });

                chatHistory.push({
                    role: "model",
                    parts: [{ text: apiResponseText }]
                });

            } catch (error) {
                console.error('Chat error:', error);
                let errorMessage = 'Đã xảy ra lỗi. Vui lòng thử lại.';

                if (error.message.includes('429')) {
                    errorMessage = 'Quá nhiều yêu cầu. Vui lòng chờ một chút.';
                } else if (error.message.includes('network')) {
                    errorMessage = 'Lỗi kết nối. Vui lòng kiểm tra internet.';
                }

                messageElement.innerText = errorMessage;
                messageElement.style.color = "#ff0000";
            } finally {
                // ✅ Reset dữ liệu file
                userData.file = {};
                incomingMessageDiv.classList.remove("thinking");
                chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });
            }
        };

        const handleOutgoingMessage = (e) => {
            e.preventDefault();
            userData.message = messageInput.value.trim();
            messageInput.value = "";
            fileUploadWrapper.classList.remove("file-uploaded");
            messageInput.dispatchEvent(new Event("input"));
            const messageContent = `<div class="message-text"></div>
                                ${userData.file.data ? `<img src="data:${userData.file.mime_type};base64,${userData.file.data}" class="attachment"/>` : ""}`;
            const outgoingMessageDiv = createMessageElement(messageContent, "user-message");
            outgoingMessageDiv.querySelector(".message-text").textContent = userData.message;
            chatBody.appendChild(outgoingMessageDiv);
            chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });
            setTimeout(() => {
                const messageContent = `<svg class="bot-avatar"xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 1024 1024">
                            <path
                                d="M738.3 287.6H285.7c-59 0-106.8 47.8-106.8 106.8v303.1c0 59 47.8 106.8 106.8 106.8h81.5v111.1c0 .7.8 1.1 1.4.7l166.9-110.6 41.8-.8h117.4l43.6-.4c59 0 106.8-47.8 106.8-106.8V394.5c0-59-47.8-106.9-106.8-106.9zM351.7 448.2c0-29.5 23.9-53.5 53.5-53.5s53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5-53.5-23.9-53.5-53.5zm157.9 267.1c-67.8 0-123.8-47.5-132.3-109h264.6c-8.6 61.5-64.5 109-132.3 109zm110-213.7c-29.5 0-53.5-23.9-53.5-53.5s23.9-53.5 53.5-53.5 53.5 23.9 53.5 53.5-23.9 53.5-53.5 53.5zM867.2 644.5V453.1h26.5c19.4 0 35.1 15.7 35.1 35.1v121.1c0 19.4-15.7 35.1-35.1 35.1h-26.5zM95.2 609.4V488.2c0-19.4 15.7-35.1 35.1-35.1h26.5v191.3h-26.5c-19.4 0-35.1-15.7-35.1-35.1zM561.5 149.6c0 23.4-15.6 43.3-36.9 49.7v44.9h-30v-44.9c-21.4-6.5-36.9-26.3-36.9-49.7 0-28.6 23.3-51.9 51.9-51.9s51.9 23.3 51.9 51.9z">
                            </path>
                        </svg>
                        <div class="message-text">
                            <div class="thinking-indicator">
                                <div class="dot"></div>
                                <div class="dot"></div>
                                <div class="dot"></div>
                            </div>
                        </div>`;
                const incomingMessageDiv = createMessageElement(messageContent, "bot-message", "thinking");
                chatBody.appendChild(incomingMessageDiv);
                chatBody.scrollTo({ top: chatBody.scrollHeight, behavior: "smooth" });
                generateBotResponse(incomingMessageDiv);
            }, 600);
        }
        messageInput.addEventListener("keydown", (e) => {
            const userMessage = e.target.value.trim();
            if (e.key === "Enter" && userMessage && !e.shiftKey && window.innerWidth > 768) {
                handleOutgoingMessage(e);
            }
        });
        messageInput.addEventListener("input", () => {
            messageInput.style.height = `${initialInputHeight}px`;
            messageInput.style.height = `${messageInput.scrollHeight}px`;
            document.querySelector(".chat-form").style.borderRadius = messageInput.scrollHeight > initialInputHeight ? "15px" : "32px";
        });
        fileInput.addEventListener("change", () => {
            const file = fileInput.files[0];
            if (!file) return;
            const reader = new FileReader();
            reader.onload = (e) => {
                fileUploadWrapper.querySelector("img").src = e.target.result;
                fileUploadWrapper.classList.add("file-uploaded");
                const base64String = e.target.result.split(",")[1];
                userData.file = {
                    data: base64String,
                    mime_type: file.type
                }
                fileInput.value = "";
            }
            reader.readAsDataURL(file);
        })
        fileCancelButton.addEventListener("click", () => {
            userData.file = {};
            fileUploadWrapper.classList.remove("file-uploaded");
        });
        const picker = new EmojiMart.Picker({
            theme: "ligth",
            skinTonePosition: "none",
            previewPosition: "none",
            onEmojiSelect: (emoji) => {
                const { selectionStart: start, selectionEnd: end } = messageInput;
                messageInput.setRangeText(emoji.native, start, end, "end");
                messageInput.focus();
            },
            onClickOutside: (e) => {
                if (e.target.id === "emoji-picker") {
                    document.body.classList.toggle("show-emoji-picker");
                } else {
                    document.body.classList.remove("show-emoji-picker");
                }
            }
        });
        document.querySelector(".chat-form").appendChild(picker);
        sendMessageButton.addEventListener("click", (e) => handleOutgoingMessage(e));
        document.querySelector("#file-upload").addEventListener("click", () => fileInput.click());
        chatbotToggler.addEventListener("click", () => document.body.classList.toggle("show-chatbot"));
        closeChatbot.addEventListener("click", () => document.body.classList.remove("show-chatbot"));
    </script>
</body>

</html>